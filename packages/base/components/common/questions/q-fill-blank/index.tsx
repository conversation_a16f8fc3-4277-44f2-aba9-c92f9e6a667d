import { defineComponent } from 'vue'
import type { PropType } from 'vue'
import type { TransformToVoQuestionData } from '@sa/utils'
import { NFormItem } from 'naive-ui'
import CKEditor from '@sa/components/common/ck-editor/index.vue'
import styles from './index.module.scss'

export default defineComponent({
  name: 'FillBlank',
  props: {
    item: {
      type: Object as PropType<TransformToVoQuestionData>,
      required: true,
    },
    modelValue: {
      type: [String, Number],
      default: '',
    },
    type: {
      type: String as PropType<'edit' | 'answer' | 'preview'>,
      default: 'answer',
    },

  },
  emits: ['update:modelValue'],
  setup(props) {
    const isdisabled = computed(() => props.type === 'preview')

    return () => {
      return (
        <div class={styles.fillBlankContainer}>
          {!isdisabled.value && (
            <ul>
              
            </ul>
            <NFormItem label="参考答案">
              {/* v-model:editorValue={analysis.value} */}
              <CKEditor></CKEditor>
            </NFormItem>
          )}
        </div>
      )
    }
  },
})
